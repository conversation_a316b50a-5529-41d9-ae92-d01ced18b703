{
    // Java settings
    "java.compile.nullAnalysis.mode": "automatic",
    "java.configuration.updateBuildConfiguration": "automatic",
    "java.format.settings.url": "https://raw.githubusercontent.com/google/styleguide/gh-pages/eclipse-java-google-style.xml",
    "java.format.settings.profile": "GoogleStyle",
    "java.completion.importOrder": [
        "java",
        "javax",
        "org",
        "com"
    ],
    "java.saveActions.organizeImports": true,
    "java.cleanup.actions": [
        "qualifyMembers",
        "qualifyStaticMembers"
    ],
    "java.sources.organizeImports.starThreshold": 3,
    "java.sources.organizeImports.staticStarThreshold": 3,
    "java.test.config": {
        "vmArgs": [
            "-Djava.util.logging.config.file=logging.properties"
        ]
    },
    "java.debug.settings.hotCodeReplace": "auto",
    "java.server.launchMode": "Standard",
    "java.inlayHints.parameterNames.enabled": "all",
    "java.codeGeneration.generateComments": true,
    "java.codeGeneration.useBlocks": true,
    "java.completion.enabled": true,
    "java.completion.guessMethodArguments": "insertBestGuessedArguments",
    "java.completion.favoriteStaticMembers": [
        "org.junit.Assert.*",
        "org.junit.Assume.*",
        "org.junit.jupiter.api.Assertions.*",
        "org.junit.jupiter.api.Assumptions.*",
        "org.junit.jupiter.api.DynamicContainer.*",
        "org.junit.jupiter.api.DynamicTest.*",
        "org.mockito.Mockito.*",
        "org.mockito.ArgumentMatchers.*",
        "org.mockito.Answers.*"
    ],
    "java.edit.smartSemicolonDetection.enabled": true,
    "java.jdt.ls.vmargs": "-XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable",
    // TypeScript settings (for React)
    "typescript.updateImportsOnFileMove.enabled": "always",
    "typescript.format.enable": true,
    "typescript.suggest.completeFunctionCalls": true,
    "typescript.preferences.importModuleSpecifier": "relative",
    "typescript.preferences.quoteStyle": "single",
    "typescript.inlayHints.functionLikeReturnTypes.enabled": true,
    "typescript.inlayHints.parameterNames.enabled": "all",
    "typescript.inlayHints.variableTypes.enabled": true,
    "typescript.suggest.autoImports": true,
    "typescript.tsdk": "node_modules/typescript/lib",
    "typescript.enablePromptUseWorkspaceTsdk": true,
    "typescript.preferences.useAliasesForRenames": true,
    "typescript.preferences.importModuleSpecifierEnding": "minimal",
    "typescript.suggest.paths": true,
    "typescript.surveys.enabled": false,
    "typescript.tsserver.log": "off",
    "typescript.tsserver.maxTsServerMemory": 4096,
    "typescript.tsserver.watchOptions": {
        "watchFile": "useFsEvents",
        "watchDirectory": "useFsEvents",
        "fallbackPolling": "dynamicPriorityPolling"
    },
    "js/ts.implicitProjectConfig.checkJs": true,
    "js/ts.implicitProjectConfig.experimentalDecorators": true,
    "js/ts.implicitProjectConfig.strictNullChecks": true,
    "js/ts.implicitProjectConfig.strictFunctionTypes": true,
    // React settings
    "emmet.includeLanguages": {
        "javascript": "javascriptreact",
        "typescript": "typescriptreact"
    },
    "emmet.triggerExpansionOnTab": true,
    "emmet.showExpandedAbbreviation": "always",
    // Docker settings
    "docker.containers.sortBy": "CreatedTime",
    "docker.containers.description": [
        "ContainerName",
        "Status",
        "Ports"
    ],
    "docker.images.sortBy": "CreatedTime",
    "docker.commands.composeUp": [
        "docker-compose",
        "-f",
        "${configurationFile}",
        "up",
        "-d",
        "--build"
    ],
    // Editor code actions
    "editor.codeActionsOnSave": {
        "source.organizeImports": "explicit"
    },
    // Formatter settings for different file types
    "[jsonc]": {
        "editor.defaultFormatter": "vscode.json-language-features"
    },
    "[java]": {
        "editor.defaultFormatter": "redhat.java"
    },
    // Editor settings
    "editor.formatOnSave": true,
    "editor.formatOnPaste": false,
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
    "editor.detectIndentation": true,
    "editor.rulers": [
        120
    ],
    "editor.suggestSelection": "first",
    "editor.wordWrap": "off",
    "editor.renderWhitespace": "boundary",
    "editor.bracketPairColorization.enabled": true,
    "editor.guides.bracketPairs": true,
    "editor.linkedEditing": true,
    "editor.minimap.maxColumn": 80,
    "editor.minimap.showSlider": "always",
    "editor.smoothScrolling": true,
    "editor.stickyScroll.enabled": true,
    "editor.guides.indentation": true,
    "editor.guides.highlightActiveIndentation": true,
    "editor.suggest.preview": true,
    "editor.suggest.showStatusBar": true,
    // Files settings
    "files.autoSave": "afterDelay",
    "files.autoSaveDelay": 1000,
    "files.exclude": {
        "**/.git": true,
        "**/.svn": true,
        "**/.hg": true,
        "**/CVS": true,
        "**/.DS_Store": true,
        "**/Thumbs.db": true,
        "**/node_modules": true,
        "**/.classpath": true,
        "**/.project": true,
        "**/.settings": true,
        "**/.factorypath": true,
        "**/target": true,
        "**/.idea": true,
        "**/.gradle": true,
        "**/dist": true,
        "**/build": true,
        "**/.next": true
    },
    "files.associations": {
        "*.properties": "properties",
        "*.yml": "yaml",
        "*.yaml": "yaml",
        "*.json": "json",
        "*.env*": "dotenv",
        "*.jsx": "javascriptreact",
        "*.tsx": "typescriptreact"
    },
    "files.eol": "\n",
    "files.insertFinalNewline": true,
    "files.trimTrailingWhitespace": true,
    "files.watcherExclude": {
        "**/.git/objects/**": true,
        "**/.git/subtree-cache/**": true,
        "**/node_modules/**": true,
        "**/target/**": true,
        "**/build/**": true,
        "**/dist/**": true
    },
    // Terminal settings
    "terminal.integrated.defaultProfile.windows": "PowerShell",
    "terminal.integrated.defaultProfile.linux": "bash",
    "terminal.integrated.defaultProfile.osx": "zsh",
    "terminal.integrated.scrollback": 5000,
    "terminal.integrated.persistentSessionReviveProcess": "never",
    "terminal.integrated.tabs.enabled": true,
    "terminal.integrated.cursorBlinking": true,
    // Git settings
    "git.autofetch": true,
    "git.confirmSync": false,
    "git.enableSmartCommit": true,
    "git.branchProtection": [
        "main",
        "master",
        "develop"
    ],
    "git.branchProtectionPrompt": "alwaysPrompt",
    "git.mergeEditor": true,
    "diffEditor.ignoreTrimWhitespace": false,
    // Explorer settings
    "explorer.compactFolders": false,
    "explorer.confirmDelete": true,
    "explorer.confirmDragAndDrop": true,
    "explorer.fileNesting.enabled": true,
    "explorer.fileNesting.patterns": {
        "*.ts": "${capture}.js, ${capture}.d.ts, ${capture}.test.ts, ${capture}.test.js, ${capture}.spec.ts, ${capture}.spec.js",
        "*.jsx": "${capture}.js, ${capture}.css, ${capture}.scss, ${capture}.test.jsx, ${capture}.test.js, ${capture}.spec.jsx, ${capture}.spec.js",
        "*.tsx": "${capture}.ts, ${capture}.css, ${capture}.scss, ${capture}.test.tsx, ${capture}.test.ts, ${capture}.spec.tsx, ${capture}.spec.ts",
        "*.java": "${capture}Test.java, ${capture}IT.java, ${capture}TestCase.java"
    },
    // Search settings
    "search.exclude": {
        "**/node_modules": true,
        "**/bower_components": true,
        "**/*.code-search": true,
        "**/target": true,
        "**/dist": true,
        "**/build": true,
        "**/.next": true,
        "**/.gradle": true
    },
    // Workbench settings
    "workbench.colorTheme": "Default Dark+",
    "workbench.iconTheme": "material-icon-theme",
    "workbench.editor.enablePreview": true,
    "workbench.editor.highlightModifiedTabs": true,
    "workbench.editor.limit.enabled": true,
    "workbench.editor.limit.value": 10,
    "workbench.startupEditor": "none",
    "workbench.editor.tabSizing": "shrink",
    "workbench.editor.decorations.badges": true,
    "workbench.editor.decorations.colors": true,
    "workbench.tree.indent": 16,
    "workbench.tree.renderIndentGuides": "always",
    "workbench.list.smoothScrolling": true,
    "workbench.commandPalette.history": 50,
    // GitHub Copilot settings
    "editor.inlineSuggest.enabled": true,
    "editor.inlineSuggest.showToolbar": "always",
    // Vibe coding settings
    "editor.fontFamily": "'JetBrains Mono', 'Fira Code', Consolas, 'Courier New', monospace",
    "editor.fontLigatures": true,
    "editor.fontSize": 14,
    "editor.lineHeight": 22,
    "editor.cursorBlinking": "phase",
    "editor.cursorStyle": "line",
    "editor.letterSpacing": 0.5,
    "workbench.colorCustomizations": {
        "editorCursor.foreground": "#64FFDA",
        "editor.lineHighlightBackground": "#1F2937",
        "editor.selectionBackground": "#3B4252",
        "editor.selectionHighlightBackground": "#4C566A80"
    },
    // Performance settings
    "extensions.ignoreRecommendations": false,

    // Spell checker settings
    "cSpell.enabled": true,
    "cSpell.customDictionaries": {
        "project-words": {
            "name": "project-words",
            "path": "${workspaceRoot}/.cspell.json",
            "description": "Words used in this project",
            "addWords": true
        },
        "custom": true
    },
    "cSpell.languageSettings": [
        {
            "languageId": "java",
            "locale": "en",
            "ignoreRegExpList": [
                "/@\\w+/",
                "/import\\s+[\\w\\.]+;/",
                "/package\\s+[\\w\\.]+;/"
            ]
        }
    ],
    "cSpell.ignorePaths": [
        "package-lock.json",
        "node_modules",
        "target",
        "*.class",
        "*.jar"
    ]
}
