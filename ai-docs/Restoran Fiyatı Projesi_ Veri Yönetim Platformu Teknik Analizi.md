

# **Restoran Fiyatı Projesi: Veri Yönetim Platformu Teknik Analizi**

## **1\. Amaç ve Kapsam**

### **1.1. <PERSON><PERSON><PERSON> Amacı**

Bu teknik analizin amacı, "Restoran Fiyatı Projesi" ka<PERSON><PERSON><PERSON><PERSON>, belirlenen kriterlere göre kapsama giren restoran ve kafelerin şube ve menü verilerini (ürün, fiyat, görsel vb.) dijital ortamda **g<PERSON><PERSON><PERSON>, doğrulanabilir ve sürdürülebilir** bir şekilde merkezi bir sisteme iletebilmelerini sağlayacak modüler bir veri yönetimi yazılım çözümünün teknik ve fonksiyonel gereksinimlerini tanımlamaktır.

### **1.2. <PERSON>jenin <PERSON>am<PERSON>**

Bu iş, restoran ve kafelerin şube ve lokasyon bazlı menü bilgilerinin sisteme giri<PERSON>, doğ<PERSON>lanmasını ve merkezi olarak yönetilmesini sağlayacak bir yazılım çözümünün teminini kapsamaktadır. Geliştirilecek sistem, farklı kullanıcı profillerinin (örneğin: restoran temsilcisi, veri giriş kontrolörü, onay yetkilisi ve sistem yöneticisi) ihtiyaçlarına cevap verecek şekilde çok katmanlı ve görev odaklı modüllerden oluşacaktır. Temel modüller; **Veri Yönetim Modülü**, **İzleme, Raporlama ve Destek Modülü** ve **Tutarlılık ve Anomali Kontrol Modülü** olarak belirlenmiştir.

## **2\. Sistem Mimarisi ve Tasarım Desenleri**

Projenin gereksinimleri, veri yazma (veri girişi, onay süreçleri) ve veri okuma (raporlama, izleme) operasyonlarının birbirinden farklı optimizasyon ihtiyaçları olduğunu göstermektedir. Bu nedenle, sistem mimarisinin esnek, ölçeklenebilir ve bakımı kolay olması hedeflenmelidir.

## **3\. Modüler Yapı ve Fonksiyonel Gereksinimler**

Sistem, şartnamede belirtildiği üzere üç ana modül üzerine inşa edilecektir.

### **3.1. Veri Yönetim Modülü**

Bu modül, sistemin veri giriş ve yaşam döngüsü yönetimi merkezidir.

#### **3.1.1. Veri Giriş Kanalları**

* **Web Arayüzü (Form Tabanlı Giriş):** Kullanıcıların şube, menü, ürün ve fiyat bilgilerini manuel olarak girebilecekleri, kullanıcı dostu formlar sunulacaktır. Bu formlar, kullanıcıyı yönlendiren ve hata yapmasını engelleyen akıllı tasarım prensiplerine sahip olacaktır.6  
* **API Entegrasyonu:** Restoranların kendi sistemlerinden (POS, envanter yönetimi vb.) otomatik veri akışı sağlamaları için **RESTful API** servisleri sunulacaktır.7

#### **3.1.2. Kullanıcı Arayüzü (UI/UX) ve Tasarım Uyumluluğu**

* **Tasarım Bütünlüğü:** Geliştirilecek arayüz, İdare'nin mevcut "Restoran Fiyatı" web platformunun tasarım dili, bileşen kütüphanesi, renk paleti ve kullanıcı deneyimi (UX/UI) standartlarına tam uyumlu olacaktır. İdare tarafından sağlanacak stil kılavuzları (style guide) esas alınacaktır.  
* **Form Tasarımı En İyi Uygulamaları:**  
  * **Tek Sütunlu Yerleşim:** Formlar, özellikle mobil uyumluluk ve okunabilirlik için tek sütunlu bir yapıda tasarlanacaktır. Bu, kullanıcıların dikkatini dağıtmadan doğrusal bir akış sağlar.8  
  * **Net Etiketleme:** Her giriş alanının üzerinde net ve anlaşılır bir etiket bulunacaktır. Alan içine yerleştirilen "placeholder" metinler etiket yerine kullanılmayacaktır, çünkü bu durum kullanıcıların hafızasını zorlar ve erişilebilirlik sorunları yaratır.10 Zorunlu olmayan alanlar "isteğe bağlı" olarak açıkça belirtilecektir.  
  * **Gruplama:** İlgili alanlar (örneğin, adres bilgileri, ürün detayları) mantıksal gruplar halinde düzenlenerek bilişsel yük azaltılacaktır.8  
  * **Anlık Doğrulama (Inline Validation):** Kullanıcı veri girerken, girişin doğruluğu anlık olarak kontrol edilecek ve geri bildirim sağlanacaktır. Hatalı girişlerde, kullanıcıyı suçlamayan, yapıcı ve yol gösterici hata mesajları gösterilecektir (örneğin, "Geçersiz e-posta adresi" yerine "Lütfen geçerli bir e-posta adresi giriniz").9

#### **3.1.3. API ve Entegrasyon**

* **RESTful Servisler:** Tüm veri işlemleri (CRUD \- Create, Read, Update, Delete) için standart HTTP metotlarını kullanan RESTful API uç noktaları sağlanacaktır.7  
* **Dokümantasyon:** Geliştiricilerin kolay entegrasyon yapabilmesi için **Swagger/OpenAPI** formatında interaktif ve detaylı bir API dokümantasyonu sunulacaktır.11  
* **Güvenlik:** API erişimi, her kullanıcı veya uygulama için özel olarak üretilen API Anahtarı (API Key) veya JWT (JSON Web Token) tabanlı bir mekanizma ile korunacaktır.7

#### **3.1.4. Veri Doğrulama (Validasyon)**

Veriler sisteme kaydedilmeden önce, hem web arayüzünde hem de API katmanında katı doğrulama kurallarından geçirilecektir. Bu, veri bütünlüğünün temelini oluşturur.

* **Kural Tanımları:** Alan adı, veri tipi (metin, sayı, tarih vb.), zorunluluk durumu, değer aralığı (örneğin, fiyat 0'dan büyük olmalı) gibi temel validasyon kuralları sistem yöneticisi tarafından tanımlanabilir olacaktır.  
* **Doğrulama Türleri:**  
  * **Sözdizimsel (Syntax) Doğrulama:** Verinin formatının doğruluğunu kontrol eder (örneğin, e-posta formatı, tarih formatı).  
  * **Anlamsal (Semantic) Doğrulama:** Verinin, tanımlı bir listede veya referans tabloda olup olmadığını kontrol eder (örneğin, girilen ilçe kodunun geçerli olması).

#### **3.1.5. Onay Süreçleri ve Yetkilendirme**

* **Çok Adımlı Onay Akışları:** Veri girişleri için çok adımlı ve rol bazlı onay süreçleri tasarlanacaktır. Örneğin, bir "Restoran Temsilcisi" tarafından girilen veri, bir "Veri Giriş Kontrolörü" tarafından incelenip son olarak bir "Onay Yetkilisi" tarafından yayınlanabilir.12  
* **Rol Bazlı Erişim Kontrolü (RBAC):** Sistem, her kullanıcı rolü için ayrıntılı yetkilendirme sağlayacaktır. Kullanıcılar yalnızca kendi rollerine atanmış işlemleri (görüntüleme, ekleme, düzenleme, silme) gerçekleştirebilecektir.14  
* **Durum Takibi ve Bildirimler:** Kullanıcılar, gönderdikleri veri setlerinin durumunu (Beklemede, Onaylandı, Reddedildi) bir gösterge paneli üzerinden takip edebilecektir. Durum değişikliklerinde (onay/ret gibi) kullanıcılara sistem içi ve/veya e-posta ile otomatik bildirimler gönderilecektir.15

#### **3.1.6. Veri ve Görsel Yönetimi**

* **Versiyonlama:** Veriler üzerinde yapılan her değişiklik, kimin tarafından ve ne zaman yapıldığı bilgisiyle birlikte versiyonlanarak saklanacaktır. Bu, denetim ve geri izlenebilirlik için kritiktir.  
* **Toplu İşlemler:** Kullanıcıların Excel veya CSV formatındaki dosyalarla toplu veri yüklemesi ve sistemdeki verileri aynı formatlarda dışa aktarması desteklenecektir.6  
* **Görsel Yönetimi:** Ürün ve şube görselleri, belirlenen kriterlere (boyut, format vb.) uygun olarak sisteme yüklenebilecek ve bu görseller de metin verileri gibi çok adımlı onay süreçlerinden geçecektir. Toplu görsel yükleme ve arşivleme özellikleri de sunulacaktır.

#### **3.1.7. Kimlik Doğrulama**

Sistem, kurumsal kimlik yönetimi sistemleriyle entegrasyonu destekleyecektir. LDAP, SSO (Single Sign-On), Active Directory (AD) ve SAML gibi standart protokollerle uyumlu çalışacaktır.

### **3.2. İzleme, Raporlama ve Destek Modülü**

Bu modül, sistemin operasyonel sağlığını ve kullanımını izlemek için merkezi bir kontrol paneli görevi görür.

* **Loglama (Günlük Kaydı):** Tüm kullanıcı eylemleri (kim, ne zaman, ne yaptı), API çağrıları ve kritik sistem olayları ayrıntılı olarak loglanacaktır. Bu loglar, güvenlik denetimleri ve hata ayıklama için esastır.  
* **Yönetici Gösterge Panelleri (Dashboard):** Sistem yöneticileri için sistemin genel kullanım istatistiklerini, API çağrı limitlerini, hata oranlarını ve performans metriklerini gösteren gerçek zamanlı paneller sağlanacaktır.  
* **Raporlama:** Sistem kullanımı, kullanıcı aktivitesi ve hata durumları hakkında periyodik veya anlık raporlar üretilebilecektir.  
* **Destek Talep Yönetimi:** Kullanıcıların sistemle ilgili sorunlarını veya taleplerini iletebilecekleri ve takip edebilecekleri entegre bir destek talep sistemi bulunacaktır.  
* **Yedekleme ve Kurtarma:** Veritabanı ve sisteme ait kritik dosyalar düzenli olarak yedeklenecek ve felaket kurtarma senaryolarına uygun bir veri kurtarma prosedürü oluşturulacaktır.

### **3.3. Tutarlılık ve Anomali Kontrol Modülü**

Bu modül, sisteme girilen verinin sadece format olarak değil, mantıksal olarak da doğruluğunu ve tutarlılığını sağlamak için tasarlanmış akıllı bir denetim katmanıdır.

#### **3.3.1. Kural Tabanlı Tutarlılık Kontrolleri**

Sistem yöneticisi tarafından tanımlanabilen bir **iş kuralları motoru (Business Rules Engine)** altyapısı kullanılacaktır.16 Bu motor, aşağıdaki gibi kontrolleri periyodik veya anlık olarak çalıştıracaktır:

* **Temel Veri Bütünlüğü:** Fiyat alanında boş, sıfır veya negatif değer olup olmadığının kontrolü.  
* **Şubeler Arası Tutarlılık:** Aynı ürünün farklı şubelerdeki fiyatları arasında mantıksız bir tutarsızlık olup olmadığının denetlenmesi.  
* **Kategori İçi Tutarlılık:** Aynı kategori altındaki benzer ürünlerin fiyatları arasında ekstrem sapmaların (örneğin, bir çorbanın fiyatının ana yemekten yüksek olması) tespiti.

#### **3.3.2. Anomali Tespiti**

Sistem, istatistiksel ve makine öğrenmesi tabanlı teknikler kullanarak normalin dışındaki veri noktalarını tespit edecektir.18

* **Eşik Bazlı Anomali:** Bir ürünün fiyatındaki değişimin, yönetici tarafından belirlenen bir yüzde veya tutar eşiğini (%20'den fazla artış gibi) aşması durumunda uyarı üretilmesi.18  
* **İstatistiksel Anomali:** Bir ürünün fiyatının, kendi tarihsel ortalamasından veya bulunduğu bölgedeki benzer ürünlerin ortalamasından istatistiksel olarak anlamlı düzeyde (örneğin, 3 standart sapma) sapması durumunda anomali olarak işaretlenmesi.20 Bu, Z-Score gibi yöntemlerle gerçekleştirilebilir.  
* **Öğrenen Modeller (İleri Seviye):** Gelecekte, Isolation Forest gibi daha gelişmiş denetimsiz öğrenme algoritmaları kullanılarak, karmaşık ve daha önce tanımlanmamış anomali kalıpları tespit edilebilir.20

#### **3.3.3. Anomali Yönetim Süreci**

* **Bildirim ve İşaretleme:** Tespit edilen bir tutarsızlık veya anomali, ilgili veri setinin "İncelemede" statüsüne alınmasını tetikleyecektir. İlgili kontrolör ve onaylayıcı kullanıcılara açıklayıcı bir uyarı mesajı ile bildirim gönderilecektir.  
* **Görselleştirme ve Raporlama:** Anomali raporları, kontrolörlerin kolayca anlayabileceği görsel arayüzler ve grafiklerle sunulacaktır. Bu raporlar, anomaliye neden olan durumu ve potansiyel etkilerini açıkça gösterecektir.  
* **Aksiyon Alma:** Yetkili kullanıcılar, bu raporlar üzerinden düzeltici aksiyonlar (veriyi reddetme, düzenleme talebi gönderme vb.) alabilecektir.

## **4\. Teknik Gereksinimler ve Teknoloji Yığını**

### **4.1. Genel Teknik Gereksinimler**

* **Platform:** Sistem tamamen web tabanlı olacak ve modern tarayıcıların (Chrome, Firefox, Safari, Edge) güncel sürümleriyle tam uyumlu çalışacaktır.  
* **Güvenlik:** Tüm iletişim HTTPS üzerinden şifrelenecektir. Kullanıcı erişim ve işlem logları tutulacak, oturum yönetimi güvenlik standartlarına uygun olacaktır.  
* **Veritabanı:** Sistem, veritabanı bağımsız bir yapıda tasarlanmalı veya şartnamede belirtilen PostgreSQL, Oracle, MSSQL veritabanlarından biriyle uyumlu olmalıdır.  
* **Ölçeklenebilirlik:** Mimari, artan kullanıcı ve veri yükünü karşılayabilecek şekilde yatayda ölçeklenebilir (horizontally scalable) tasarlanacaktır.

### **4.2. Önerilen Teknoloji Yığını (Technology Stack)**

Bu, projenin gereksinimlerine uygun, modern ve kanıtlanmış teknolojilerden oluşan bir öneridir 22:

* **Sunum Katmanı (Frontend):** React, Angular veya Vue.js gibi modern bir JavaScript kütüphanesi/çerçevesi kullanılarak dinamik, bileşen tabanlı ve kullanıcı dostu bir arayüz geliştirilebilir.  
* **Uygulama Katmanı (Backend):** Java (Spring Boot) veya.NET Core gibi kurumsal düzeyde yaygın olarak kullanılan, güçlü ve olgun ekosistemlere sahip teknolojiler tercih edilebilir. Bu platformlar, güvenlik, performans ve ölçeklenebilirlik gereksinimlerini karşılamada güçlüdür.  
* **Veri Katmanı (Data Layer):**  
  * **Ana Veritabanı:** PostgreSQL (açık kaynak ve güçlü), Oracle veya MSSQL (kurumsal standartlar).  
  * **Önbellekleme (Caching):** Sık erişilen veriler ve oturum bilgileri için Redis gibi bir in-memory veritabanı kullanılarak sistem performansı artırılabilir.  
* **API Gateway:** Tüm API isteklerini yönetmek, güvenliği merkezileştirmek ve hız limitlendirme (rate limiting) gibi politikaları uygulamak için bir API Gateway kullanılabilir.  
* **Olay Kuyruğu (Message Broker):** Olay güdümlü mimariyi desteklemek için RabbitMQ veya Apache Kafka gibi bir mesajlaşma sistemi entegre edilebilir.

## **5\. Kurulum ve Sorumluluklar**

Şartnamenin 2.7.7. maddesi uyarınca, geliştirilecek yazılımın tüm modüllerinin İdare tarafından sağlanacak donanım ve sistem yazılımı altyapısı üzerine kurulması, konfigürasyonlarının yapılması ve sistemin çalışır halde teslim edilmesi Yüklenici'nin sorumluluğundadır. Bu süreç, gerektiğinde İdare personeli ile tam bir eşgüdüm içinde yürütülecektir.

#### **Alıntılanan çalışmalar**

1. CQRS Pattern \- Azure Architecture Center | Microsoft Learn, erişim tarihi Haziran 24, 2025, [https://learn.microsoft.com/en-us/azure/architecture/patterns/cqrs](https://learn.microsoft.com/en-us/azure/architecture/patterns/cqrs)  
2. Data Pipeline Architecture: 5 Design Patterns with Examples \- Dagster, erişim tarihi Haziran 24, 2025, [https://dagster.io/guides/data-pipeline/data-pipeline-architecture-5-design-patterns-with-examples](https://dagster.io/guides/data-pipeline/data-pipeline-architecture-5-design-patterns-with-examples)  
3. Software Architecture Patterns: Driving Scalability and Performance \- Maruti Techlabs, erişim tarihi Haziran 24, 2025, [https://marutitech.com/software-architecture-patterns/](https://marutitech.com/software-architecture-patterns/)  
4. Demystifying Data Architecture Patterns and Data Modeling: A Comprehensive Guide (Part 3\) \- Wednesday Solutions, erişim tarihi Haziran 24, 2025, [https://www.wednesday.is/writing-articles/demystifying-data-architecture-patterns-and-data-modeling-a-comprehensive-guide-part-3](https://www.wednesday.is/writing-articles/demystifying-data-architecture-patterns-and-data-modeling-a-comprehensive-guide-part-3)  
5. Data Integration Architecture: Modern Design Patterns \- Nexla, erişim tarihi Haziran 24, 2025, [https://nexla.com/data-integration-101/data-integration-architecture/](https://nexla.com/data-integration-101/data-integration-architecture/)  
6. Data Entry Optimization Strategies to Ensure Accuracy, erişim tarihi Haziran 24, 2025, [https://getdatabees.com/data-entry-optimization-strategies/](https://getdatabees.com/data-entry-optimization-strategies/)  
7. Jotform API, erişim tarihi Haziran 24, 2025, [https://api.jotform.com/docs/](https://api.jotform.com/docs/)  
8. How to Design UI Forms in 2025: Your Best Guide | IxDF, erişim tarihi Haziran 24, 2025, [https://www.interaction-design.org/literature/article/ui-form-design](https://www.interaction-design.org/literature/article/ui-form-design)  
9. 12 form design best practices for 2023 \- Adobe Experience Cloud, erişim tarihi Haziran 24, 2025, [https://business.adobe.com/blog/basics/form-design-best-practices](https://business.adobe.com/blog/basics/form-design-best-practices)  
10. Form design best practices by Andrew Coyle, erişim tarihi Haziran 24, 2025, [https://www.andrewcoyle.com/blog/form-design-best-practices](https://www.andrewcoyle.com/blog/form-design-best-practices)  
11. Typeform's developer portal \- SETCorrect LLC, erişim tarihi Haziran 24, 2025, [https://www.setcorrect.com/portfolio/work1/](https://www.setcorrect.com/portfolio/work1/)  
12. How to Design Multi-Step Forms that Enhance the User Experience | Designlab, erişim tarihi Haziran 24, 2025, [https://designlab.com/blog/design-multi-step-forms-enhance-user-experience](https://designlab.com/blog/design-multi-step-forms-enhance-user-experience)  
13. How to create and optimize workflow approval process \- Aproove, erişim tarihi Haziran 24, 2025, [https://www.aproove.com/blog/how-to-create-and-optimize-an-approval-process](https://www.aproove.com/blog/how-to-create-and-optimize-an-approval-process)  
14. Rules Engine Design Pattern: A Guide on Architecture and Design ..., erişim tarihi Haziran 24, 2025, [https://www.nected.ai/us/blog-us/rules-engine-design-pattern](https://www.nected.ai/us/blog-us/rules-engine-design-pattern)  
15. Creating an Effective Design Approval Process | Wrangle Blog, erişim tarihi Haziran 24, 2025, [https://www.wrangle.io/post/creating-an-effective-design-approval-process](https://www.wrangle.io/post/creating-an-effective-design-approval-process)  
16. Best Practices for Implementing a Business Rules Engine, erişim tarihi Haziran 24, 2025, [https://rulesengine.dev/article/Best\_Practices\_for\_Implementing\_a\_Business\_Rules\_Engine.html](https://rulesengine.dev/article/Best_Practices_for_Implementing_a_Business_Rules_Engine.html)  
17. A Comprehensive Guide to Understand Business Rules Engine \- Cflow, erişim tarihi Haziran 24, 2025, [https://www.cflowapps.com/business-rules-engine/](https://www.cflowapps.com/business-rules-engine/)  
18. Anomaly Detection: How Algorithms Spot Cyber Threats | Fidelis ..., erişim tarihi Haziran 24, 2025, [https://fidelissecurity.com/threatgeek/network-security/anomaly-detection-algorithms/](https://fidelissecurity.com/threatgeek/network-security/anomaly-detection-algorithms/)  
19. Anomaly Detection Techniques: How to Uncover Risks, Identify Patterns, and Strengthen Data Integrity \- MindBridge, erişim tarihi Haziran 24, 2025, [https://www.mindbridge.ai/blog/anomaly-detection-techniques-how-to-uncover-risks-identify-patterns-and-strengthen-data-integrity/](https://www.mindbridge.ai/blog/anomaly-detection-techniques-how-to-uncover-risks-identify-patterns-and-strengthen-data-integrity/)  
20. Anomaly Detection in Stock Prices | Z-Score, Isolation Forest & Residuals \- YouTube, erişim tarihi Haziran 24, 2025, [https://www.youtube.com/watch?v=Z\_iasbvduuM](https://www.youtube.com/watch?v=Z_iasbvduuM)  
21. Anomaly Detection: What You Need To Know \- BMC Software, erişim tarihi Haziran 24, 2025, [https://www.bmc.com/learn/anomaly-detection.html](https://www.bmc.com/learn/anomaly-detection.html)  
22. What is the Modern Data Stack? Diagram and Examples \- Qlik, erişim tarihi Haziran 24, 2025, [https://www.qlik.com/us/data-integration/modern-data-stack](https://www.qlik.com/us/data-integration/modern-data-stack)  
23. Figuring Out the Best Tech Stack for Your Software Development Project \- Twenty Ideas, erişim tarihi Haziran 24, 2025, [https://www.twentyideas.com/blog/choosing-a-technology-stack](https://www.twentyideas.com/blog/choosing-a-technology-stack)  
24. The Modern Data Stack (updated for 2021\) \- Metabase, erişim tarihi Haziran 24, 2025, [https://www.metabase.com/blog/The-Modern-Data-Stack](https://www.metabase.com/blog/The-Modern-Data-Stack)